from langchain_core.prompts import ChatPromptTemplate, HumanMessagePromptTemplate, SystemMessagePromptTemplate

from agent.tools import (
    firecrawl_scrape,
    tavily_crawl,
    tavily_search,
)
from agent.tools.linkedin import get_company_details
from utils import init_model
from utils.file_handler import load_file
from zoho.accounts_api import ZohoAccountInfo


async def analyze_account(account_info: ZohoAccountInfo) -> str:
    """
    Analyze the account info and return a markdown formatted analysis report.

    Args:
        account_info: ZohoAccountInfo object containing company information

    Returns:
        str: Markdown formatted analysis report in English
    """
    # create llm agent to analyze the account info

    tools = [tavily_search, tavily_crawl, firecrawl_scrape, get_company_details]

    llm = init_model(
        model="gemini-2.5-flash",
        max_tokens=10240,
        temperature=0.2,
        thinking_budget=256,
        include_thoughts=True,
    )

    llm.bind_tools(tools)

    # 加载分析 prompt
    system_prompt = load_file("agent/account/prompts/analysis_company.md")

    # 加载映翰通公司简介
    inhand_introduction = load_file("prompts/inhand_introduction.md")

    if not system_prompt:
        raise ValueError("system_prompt is None")

    if not inhand_introduction:
        raise ValueError("inhand_introduction is None")

    # 将映翰通简介插入到系统提示词中
    system_prompt = system_prompt.format(inhand_introduction=inhand_introduction)

    # 构建提示词
    prompt = ChatPromptTemplate.from_messages(
        [
            SystemMessagePromptTemplate.from_template(system_prompt),
            HumanMessagePromptTemplate.from_template("account_info: {account_info}"),
        ]
    )

    chain = prompt | llm

    result = await chain.ainvoke({"account_info": account_info})

    # 直接提取并返回文本内容
    if hasattr(result, "content"):
        if isinstance(result.content, list):
            # 过滤掉 thinking 类型的内容，只保留文本内容
            text_content = []
            for item in result.content:
                if isinstance(item, dict) and item.get("type") == "text":
                    text_content.append(item.get("text", ""))
                elif isinstance(item, str):
                    text_content.append(item)
            return "\n".join(text_content)
        else:
            return str(result.content)
    else:
        return str(result)
